1751719149O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:24:{i:0;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:1;s:4:"name";s:10:"superAdmin";s:5:"image";s:85:"https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg";}s:11:" * original";a:3:{s:2:"id";i:1;s:4:"name";s:10:"superAdmin";s:5:"image";s:85:"https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:1;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:2;s:4:"name";s:10:"childUser1";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:2;s:4:"name";s:10:"childUser1";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:2;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:3;s:4:"name";s:10:"childUser2";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:3;s:4:"name";s:10:"childUser2";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:3;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:6;s:4:"name";s:6:"shivam";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:6;s:4:"name";s:6:"shivam";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:4;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:7;s:4:"name";s:11:"grandChild1";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:7;s:4:"name";s:11:"grandChild1";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:5;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:8;s:4:"name";s:11:"grandChild2";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:8;s:4:"name";s:11:"grandChild2";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:6;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:10;s:4:"name";s:15:"greatGrandChild";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:10;s:4:"name";s:15:"greatGrandChild";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:7;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:11;s:4:"name";s:8:"managerA";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:11;s:4:"name";s:8:"managerA";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:8;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:12;s:4:"name";s:10:"employeeA1";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:12;s:4:"name";s:10:"employeeA1";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:9;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:13;s:4:"name";s:10:"employeeA2";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:13;s:4:"name";s:10:"employeeA2";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:10;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:14;s:4:"name";s:13:"employeeRoot1";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:14;s:4:"name";s:13:"employeeRoot1";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:11;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:15;s:4:"name";s:13:"employeeRoot2";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:15;s:4:"name";s:13:"employeeRoot2";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:12;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:16;s:4:"name";s:10:"employeeA3";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:16;s:4:"name";s:10:"employeeA3";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:13;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:17;s:4:"name";s:10:"employeeA4";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:17;s:4:"name";s:10:"employeeA4";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:14;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:18;s:4:"name";s:13:"superAdmin323";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:18;s:4:"name";s:13:"superAdmin323";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:15;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:19;s:4:"name";s:11:"childUser12";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:19;s:4:"name";s:11:"childUser12";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:16;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:20;s:4:"name";s:2:"22";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:20;s:4:"name";s:2:"22";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:17;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:21;s:4:"name";s:9:"shivam333";s:5:"image";s:85:"https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg";}s:11:" * original";a:3:{s:2:"id";i:21;s:4:"name";s:9:"shivam333";s:5:"image";s:85:"https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:18;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:22;s:4:"name";s:4:"4444";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:22;s:4:"name";s:4:"4444";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:19;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:23;s:4:"name";s:14:"grandChild2444";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:23;s:4:"name";s:14:"grandChild2444";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:20;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:24;s:4:"name";s:19:"greatGrandChild4444";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:24;s:4:"name";s:19:"greatGrandChild4444";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:21;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:25;s:4:"name";s:13:"managerA44444";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:25;s:4:"name";s:13:"managerA44444";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:22;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:26;s:4:"name";s:14:"employeeA14444";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:26;s:4:"name";s:14:"employeeA14444";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:23;O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:4:"user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:2:"id";i:27;s:4:"name";s:14:"employeeA24444";s:5:"image";N;}s:11:" * original";a:3:{s:2:"id";i:27;s:4:"name";s:14:"employeeA24444";s:5:"image";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:3:{i:0;s:8:"password";i:1;s:14:"remember_token";i:2;s:22:"profileDataForPhpMaker";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:19:" * authPasswordName";s:8:"password";s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}}s:28:" * escapeWhenCastingToString";b:0;}