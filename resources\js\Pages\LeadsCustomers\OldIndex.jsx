import Main from "@/Layouts/Main";
import { button_custom, customDrawer, table_custom } from "@/Pages/Helpers/DesignHelper";
import { Head } from "@inertiajs/react";
import { Avatar, Badge, Button, Checkbox, Drawer, Table, Tooltip } from "flowbite-react";
import $ from "jquery";
import { useState } from "react";
import { FaLongArrowAltRight } from "react-icons/fa";
import { IoIosAddCircle, IoIosArrowDown, IoMdAdd } from "react-icons/io";
import {
    MdOutlineGroupAdd,
    MdOutlineModeEditOutline,
    MdOutlinePermContactCalendar,
    MdOutlinePlagiarism
} from "react-icons/md";
import { PiExportBold } from "react-icons/pi";
import { RiDeleteBin6Line, RiHistoryFill } from "react-icons/ri";
import { TbColumns3 } from "react-icons/tb";
import Add from "./Add";
import AssignLead from "./AssignLead";
import Details from "./Details";
import IndexDetails from "./Details/IndexDetails";
import Edit from "./Edit";
import Followup from "./Followup";
import LeadContact from "./LeadContact";
import SideMenu from "./SideMenu";
import TabBar from "./TabBar";


function OldIndex({ collection }) {

    const [isCheckAll, setIsCheckAll] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');

    const [agents, setAgents] = useState([
        { name: 'Abhishek', avatar: 'https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg' },
        { name: 'Ajit Singh', avatar: 'https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg' },
        { name: 'Aman', avatar: 'https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg' },
        { name: 'Ambika Modi', avatar: 'https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg' },
        { name: 'Amit Modi', avatar: 'https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg' },
        { name: 'Anjali', avatar: 'https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg' },
        { name: 'Bhawna Tanwar', avatar: 'https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg' },
        { name: 'Deepali', avatar: 'https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg' },
    ]);

    const [isDrawerOpen, setIsDrawerOpen] = useState({
        leadContact: false,
        followup: false,
        details: false,
        assignLead: false,
        addLead: false,
        editLead: false,
    });

    const filteredAgents = agents.filter(agent =>
        agent.name.toLowerCase().includes(searchTerm.toLowerCase())
    );



    // Toggle Row
    const ToggleChild = (parent, child) => {
        var parentEle = $("#" + parent);
        var ele = $("#" + child);
        if (ele.hasClass("hidden")) {
            parentEle.addClass("rotate-180");
            ele.removeClass("hidden");
        } else {
            parentEle.removeClass("rotate-180");
            ele.addClass("hidden");
        }
    };

    return (
        <Main>
            <Head title="Leads & Customers" />
            <div className="relative overflow-hidden p-2">
                <div className="grid grid-cols-12 md:grid-cols-12 sm:grid-cols-1 gap-2 grid-flow-row-dense bg-slate-100 p-0 border-none">
                    <div className="lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex hidden">
                        <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full overflow-auto min-h-[90vh] max-h-[90vh]">
                            <SideMenu />
                        </div>
                    </div>

                    <div className="dark:text-gray-400 lg:p-0 pt-0 lg:col-span-10 md:col-span-12 col-span-full relative">
                        <div className="col-span-full mb-2">
                            <TabBar />
                        </div>
                        <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                            <div className="flex justify-between p-2">
                                <div className="flex items-center gap-1">
                                    <Button.Group>
                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                        >
                                            <div className="flex items-center gap-1">
                                                <RiDeleteBin6Line className="text-slate-500" />
                                                <span className="text-xs">
                                                    Delete
                                                </span>
                                            </div>
                                        </Button>

                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                            id="dropdownInformationButton"
                                            data-dropdown-toggle="dropdownNotification"
                                            type="button"
                                        >
                                            <div className="flex items-center gap-1">
                                                <TbColumns3 className="text-slate-500" />
                                                <span className="text-xs">
                                                    Columns
                                                </span>
                                            </div>
                                        </Button>
                                    </Button.Group>
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                    >
                                        <div className="flex items-center gap-1">
                                            <PiExportBold className="text-slate-500" />
                                            <span className="text-xs">
                                                Export
                                            </span>
                                        </div>
                                    </Button>
                                </div>

                                <Button.Group className="">
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                        onClick={() =>
                                            setIsDrawerOpen((prev) => ({
                                                ...prev,
                                                assignLead: !prev.assignLead
                                            }))
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            <IoMdAdd className="text-slate-500" />
                                            <span className="text-xs">
                                                Assign
                                            </span>
                                        </div>
                                    </Button>
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                        onClick={() =>
                                            setIsDrawerOpen((prev) => ({
                                                ...prev,
                                                addLead: !prev.addLead
                                            }))
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            <IoMdAdd className="text-slate-500" />
                                            <span className="text-xs">
                                                Add Lead
                                            </span>
                                        </div>
                                    </Button>
                                </Button.Group>
                            </div>

                            <div className="table-responsive overflow-x-scroll bg-white border rounded-lg">
                                <Table hoverable theme={table_custom} className="text-nowrap">
                                    <Table.Head className="bg-slate-100">
                                        <Table.HeadCell>
                                            <Checkbox color="blue"
                                                checked={isCheckAll}
                                                onChange={() =>
                                                    setIsCheckAll(!isCheckAll)
                                                }
                                            />
                                        </Table.HeadCell>

                                        <Table.HeadCell>
                                            Name
                                        </Table.HeadCell>

                                        <Table.HeadCell>Assigned</Table.HeadCell>

                                        <Table.HeadCell>
                                            Next Follow up
                                        </Table.HeadCell>

                                        <Table.HeadCell>
                                            <h3>Status</h3>
                                        </Table.HeadCell>

                                        <Table.HeadCell>
                                            <h3>Reason / Comment</h3>
                                        </Table.HeadCell>
                                    </Table.Head>

                                    <Table.Body className="divide-y">
                                        <Table.Row className="">
                                            <Table.Cell>
                                                <Checkbox color="blue" className="rowCheckBox" />
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex justify-between ">
                                                    <div className="flex flex-col">
                                                        <div className="text-blue-600 text-sm ">
                                                            Ruchika Khatri (2568)
                                                        </div>
                                                        <div className="text-xs">
                                                            Dunes Factory Pvt. Ltd.
                                                        </div>
                                                    </div>
                                                    <Tooltip
                                                        className="p-1 px-2 bg-slate-700"
                                                        content="Approval Pending">
                                                        <div className="bg-red-600 h-fit p-0.5 rounded-full">
                                                            <RiHistoryFill className="text-white text-xl" />
                                                        </div>
                                                    </Tooltip>

                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="relative">
                                                    <Button
                                                        size="xxs"
                                                        color="transparentForTab"
                                                        onClick={() => setIsDropdownOpen(prev => !prev)}
                                                    >
                                                        <IoIosAddCircle className="text-blue-600 text-4xl" />
                                                    </Button>
                                                    {isDropdownOpen && (
                                                        <div className="absolute z-10 w-64 bg-white rounded-lg shadow-lg mt-2 p-2" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                                                            <input
                                                                type="text"
                                                                className="w-full px-3 py-2 mb-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                                placeholder="Search Agent..."
                                                                size={"xs"}
                                                                value={searchTerm}
                                                                onChange={(e) => setSearchTerm(e.target.value)}
                                                                autoFocus
                                                            />
                                                            {filteredAgents.map((agent, index) => (
                                                                <button
                                                                    key={index}
                                                                    className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg"
                                                                    onClick={() => setIsDropdownOpen(false)}
                                                                >
                                                                    <Avatar
                                                                        img={agent.avatar}
                                                                        size="sm"
                                                                        rounded
                                                                    />
                                                                    <span>{agent.name}</span>
                                                                </button>
                                                            ))}
                                                            {filteredAgents.length === 0 && (
                                                                <div className="text-gray-500 text-sm text-center py-2">No agents found</div>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell onClick={() =>
                                                setIsDrawerOpen((prev) => ({
                                                    ...prev,
                                                    followup: !prev.followup
                                                }))
                                            } className="cursor-pointer whitespace-nowrap dark:text-white">
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        12/08/24
                                                    </div>
                                                    <div className="text-xs">
                                                        2:24 PM
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <Badge className="w-fit text-nowrap" color="indigo">New Lead</Badge>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex gap-2 justify-between ">
                                                    <div className="">
                                                        <div className="text-sm">
                                                            Better follow-up needed to improve lead interaction.
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-2 h-fit text-nowrap">
                                                        {[
                                                            {
                                                                icon: <MdOutlinePermContactCalendar />,
                                                                text: "Lead Contact",
                                                                color: "blue",
                                                                onClick: () => setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    leadContact: !prev.leadContact
                                                                }))
                                                            },
                                                            {
                                                                icon: <MdOutlineModeEditOutline />,
                                                                text: "Edit",
                                                                color: "green",
                                                                onClick: () => setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    editLead: !prev.editLead
                                                                }))
                                                            },
                                                            {
                                                                icon: <MdOutlinePlagiarism />,
                                                                text: "Details",
                                                                color: "orange",
                                                                onClick: () => setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    details: !prev.details
                                                                }))
                                                            },
                                                            {
                                                                icon: <RiDeleteBin6Line />,
                                                                text: "Delete",
                                                                color: "failure",
                                                                onClick: () => setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    deleteLead: !prev.deleteLead
                                                                }))
                                                            }
                                                        ].map((btn, index) => (
                                                            <Button
                                                                key={index}
                                                                theme={button_custom}
                                                                color={btn.color}
                                                                size="xs"
                                                                onClick={btn.onClick}
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    {btn.icon}
                                                                    <span className="text-xs">{btn.text}</span>
                                                                </div>
                                                            </Button>
                                                        ))}
                                                    </div>
                                                    <div>
                                                        <button
                                                            className="p-1.5"
                                                            id="parentID"
                                                            onClick={() =>
                                                                ToggleChild(
                                                                    "parentID",
                                                                    "childTr"
                                                                )
                                                            }
                                                        >
                                                            <IoIosArrowDown />
                                                        </button>
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                        </Table.Row>
                                        <Table.Row
                                            className="hidden bg-gray-100 transition duration-150 ease-in-out"
                                            id="childTr"
                                        >
                                            <Table.Cell colSpan={9}>
                                                <Details></Details>
                                            </Table.Cell>
                                        </Table.Row>
                                        <Table.Row className="">
                                            <Table.Cell>
                                                <Checkbox color="blue" className="rowCheckBox" />
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex justify-between ">
                                                    <div className="flex flex-col">
                                                        <div className="text-blue-600 text-sm ">
                                                            Ruchika Khatri (2568)
                                                        </div>
                                                        <div className="text-xs">
                                                            Dunes Factory Pvt. Ltd.
                                                        </div>
                                                    </div>
                                                    <Tooltip
                                                        className="p-1 px-2 bg-slate-700"
                                                        content="Approval Pending">
                                                        <div className="bg-red-600 h-fit p-0.5 rounded-full">
                                                            <RiHistoryFill className="text-white text-xl" />
                                                        </div>
                                                    </Tooltip>

                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex gap-1 items-center">
                                                    <Tooltip
                                                        content="Abhishek"
                                                        className="bg-slate-700 p-1"
                                                    >
                                                        <Avatar size="md" img="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg" alt="avatar of Jese" rounded />
                                                    </Tooltip>
                                                    <FaLongArrowAltRight className="text-red-600" />
                                                    <Tooltip
                                                        content="Abhishek"
                                                        className="bg-slate-700 p-1"
                                                    >
                                                        <Avatar size="md" img="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg" alt="avatar of Jese" rounded />
                                                    </Tooltip>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        12/08/24
                                                    </div>
                                                    <div className="text-xs">
                                                        2:24 PM
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <Badge className="w-fit text-nowrap" color="indigo">New Lead</Badge>
                                            </Table.Cell>

                                            <Table.Cell>
                                                <div className="flex gap-2 justify-between ">
                                                    <div className="">
                                                        <div className="text-sm">
                                                            Better follow-up needed to improve lead interaction.
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-2 h-fit text-nowrap">
                                                        {/* <Tooltip
                                                                content="Documents"
                                                                className="bg-slate-700 p-1 px-2"
                                                            > */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    leadContact: !prev.leadContact
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePermContactCalendar />
                                                                <span className="text-xs">Lead Contact</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="green"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    editLead: !prev.editLead
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineModeEditOutline />
                                                                <span className="text-xs">Edit</span>
                                                            </div>
                                                        </Button>
                                                        {/* </Tooltip> */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    details: !prev.details
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePlagiarism />
                                                                <span className="text-xs">Details</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    deleteLead: !prev.deleteLead
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <RiDeleteBin6Line />
                                                                <span className="text-xs">Delete</span>
                                                            </div>
                                                        </Button>

                                                    </div>
                                                    <div>
                                                        <button
                                                            className="p-1.5"
                                                            id="parentID"
                                                            onClick={() =>
                                                                ToggleChild(
                                                                    "parentID",
                                                                    "childTr"
                                                                )
                                                            }
                                                        >
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                height="23px"
                                                                viewBox="0 0 24 24"
                                                                width="23px"
                                                                className="fill-gray-500"
                                                            >
                                                                <path
                                                                    d="M0 0h24v24H0V0z"
                                                                    fill="none"
                                                                />
                                                                <path d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                        </Table.Row>
                                        <Table.Row className="">
                                            <Table.Cell>
                                                <Checkbox color="blue" className="rowCheckBox" />
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex justify-between gap-2">
                                                    <div className="flex flex-col">
                                                        <div className="text-blue-600 text-sm ">
                                                            Ruchika Khatri (2568)
                                                        </div>
                                                        <div className="text-xs">
                                                            Dunes Factory Pvt. Ltd.
                                                        </div>
                                                    </div>
                                                    <Tooltip
                                                        className="p-1 px-2 bg-slate-700"
                                                        content="Approval Pending">
                                                        <div className="bg-red-600 h-fit p-0.5 rounded-full">
                                                            <RiHistoryFill className="text-white text-xl" />
                                                        </div>
                                                    </Tooltip>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex gap-1 items-center">
                                                    <Tooltip
                                                        content="Abhishek"
                                                        className="bg-slate-700 p-1"
                                                    >
                                                        <Avatar
                                                            size="sm"
                                                            img="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg"
                                                            className="rounded-full"
                                                        />
                                                    </Tooltip>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        12/08/24
                                                    </div>
                                                    <div className="text-xs">
                                                        2:24 PM
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <Badge className="w-fit" color="indigo">New Lead</Badge>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex gap-2 justify-between ">
                                                    <div className="">
                                                        <div className="text-sm">
                                                            Better follow-up needed to improve lead interaction.
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-2 h-fit text-nowrap">
                                                        {/* <Tooltip
                                                                content="Documents"
                                                                className="bg-slate-700 p-1 px-2"
                                                            > */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    leadContact: !prev.leadContact
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePermContactCalendar />
                                                                <span className="text-xs">Lead Contact</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="green"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    editLead: !prev.editLead
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineModeEditOutline />
                                                                <span className="text-xs">Edit</span>
                                                            </div>
                                                        </Button>
                                                        {/* </Tooltip> */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    details: !prev.details
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePlagiarism />
                                                                <span className="text-xs">Details</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    deleteLead: !prev.deleteLead
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <RiDeleteBin6Line />
                                                                <span className="text-xs">Delete</span>
                                                            </div>
                                                        </Button>

                                                    </div>
                                                    <div>
                                                        <button
                                                            className="p-1.5"
                                                            id="parentID"
                                                            onClick={() =>
                                                                ToggleChild(
                                                                    "parentID",
                                                                    "childTr"
                                                                )
                                                            }
                                                        >
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                height="23px"
                                                                viewBox="0 0 24 24"
                                                                width="23px"
                                                                className="fill-gray-500"
                                                            >
                                                                <path
                                                                    d="M0 0h24v24H0V0z"
                                                                    fill="none"
                                                                />
                                                                <path d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                        </Table.Row>
                                        <Table.Row className="hidden" id="childTr">
                                            <Table.Cell colSpan={7}>
                                                <Details></Details>
                                            </Table.Cell>
                                        </Table.Row>
                                    </Table.Body>
                                </Table>
                            </div>
                        </div>

                        <div className="float-end border absolute bottom-0 rounded-b-lg p-3 w-full bg-white ">
                            <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                <div className="flex gap-3 items-center">
                                    <div className="text-gray-400 text-sm ">
                                        Showing 1 to 25 of 62 entries
                                    </div>
                                    <div>
                                        <select
                                            id="countries"
                                            className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        >
                                            <option
                                                value={10}
                                                defaultValue={10}
                                            >
                                                10
                                            </option>
                                            <option value={15}>15</option>
                                            <option value={20}>20</option>
                                            <option value={25}>25</option>
                                            <option value={30}>30</option>
                                        </select>
                                    </div>
                                </div>
                                <div className="flex">
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="blue"
                                        className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                    >
                                        1
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border text-blue-600 px-2.5 rounded-none text-xs "
                                    >
                                        2
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {
                isDrawerOpen.leadContact ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.leadContact}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, leadContact: !prev.leadContact }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <Drawer.Header className="h-0" titleIcon="false" />
                        <Drawer.Items>
                            <div className="bg-white p-2">
                                <div className="flex items-start justify-between flex-wrap">
                                    <div className="flex flex-col">
                                        <span className="text-blue-600 text-base font-medium">Ruchika Khatri (2568)</span>
                                        <span className="text-sm ">Dunes Factory Pvt. Ltd.</span>
                                        <div className="text-black text-sm">
                                            <span className="font-medium me-1">Reason:</span>
                                            <span>
                                                She need ₹1200/- WhatsApp cloud for 1 year.
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex items-start gap-2 me-7">
                                            <div>
                                                <h4 className="text-base font-medium text-blue-600">
                                                    24/03/2024
                                                </h4>
                                                <h6 className="text-xs">
                                                    02:45 PM
                                                </h6>
                                            </div>
                                            <img
                                                className="rounded-full h-12 w-12"
                                                src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                alt=""
                                            />
                                            {/* <Button theme={button_custom} size="xxs" color="transparentForTab"
                                            onClick={handleClose}>
                                            <IoClose className="text-slate-500 text-2xl" />
                                        </Button> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <LeadContact />
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* Followup */}
            {
                isDrawerOpen.followup ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.followup}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, followup: !prev.followup }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <Drawer.Header className="h-0" titleIcon="false" />
                        <Drawer.Items>
                            <div className="bg-white p-2">
                                <div className="flex items-start justify-between flex-wrap">
                                    <div className="flex flex-col">
                                        <span className="text-blue-600 text-base font-medium">Ruchika Khatri (2568)</span>
                                        <span className="text-sm ">Dunes Factory Pvt. Ltd.</span>
                                        <div className="text-black text-sm">
                                            <span className="font-medium me-1">Reason:</span>
                                            <span>
                                                She need ₹1200/- WhatsApp cloud for 1 year.
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex items-start gap-2 me-7 pe-3">
                                            <div>
                                                <h4 className="text-base font-medium text-blue-600">
                                                    24/03/2024
                                                </h4>
                                                <h6 className="text-xs">
                                                    02:45 PM
                                                </h6>
                                            </div>
                                            <img
                                                className="rounded-full h-12 w-12"
                                                src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                alt=""
                                            />
                                            {/* <Button theme={button_custom} size="xxs" color="transparentForTab"
                                            onClick={handleClose}>
                                            <IoClose className="text-slate-500 text-2xl" />
                                        </Button> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <Followup />
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* Details */}
            {
                isDrawerOpen.details ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.details}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, details: !prev.details }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <Drawer.Header className="h-0" titleIcon="false" />
                        <Drawer.Items>
                            <div className="bg-white p-2">
                                <div className="flex items-start justify-between flex-wrap">
                                    <div className="flex flex-col">
                                        <span className="text-blue-600 text-base font-medium">Ruchika Khatri (2568)</span>
                                        <span className="text-sm ">Dunes Factory Pvt. Ltd.</span>
                                        <div className="text-black text-sm">
                                            <span className="font-medium me-1">Reason:</span>
                                            <span>
                                                She need ₹1200/- WhatsApp cloud for 1 year.
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex items-start gap-2 me-7 pe-3">
                                            <div>
                                                <h4 className="text-base font-medium text-blue-600">
                                                    24/03/2024
                                                </h4>
                                                <h6 className="text-xs">
                                                    02:45 PM
                                                </h6>
                                            </div>
                                            <img
                                                className="rounded-full h-12 w-12"
                                                src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                alt=""
                                            />
                                            {/* <Button theme={button_custom} size="xxs" color="transparentForTab"
                                            onClick={handleClose}>
                                            <IoClose className="text-slate-500 text-2xl" />
                                        </Button> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <IndexDetails />
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* Assign Leads */}
            {
                isDrawerOpen.assignLead ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.assignLead}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, assignLead: !prev.assignLead }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <Drawer.Header className="h-0" titleIcon="false" />
                        <Drawer.Items>
                            <div className="bg-white p-2">
                                <div className="flex items-start justify-between flex-wrap">
                                    <div className="flex flex-col">
                                        <span className="text-blue-600 text-base font-medium">Ruchika Khatri (2568)</span>
                                        <span className="text-sm ">Dunes Factory Pvt. Ltd.</span>
                                        <div className="text-black text-sm">
                                            <span className="font-medium me-1">Reason:</span>
                                            <span>
                                                She need ₹1200/- WhatsApp cloud for 1 year.
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex items-start gap-2 me-7 pe-3">
                                            <div>
                                                <h4 className="text-base font-medium text-blue-600">
                                                    24/03/2024
                                                </h4>
                                                <h6 className="text-xs">
                                                    02:45 PM
                                                </h6>
                                            </div>
                                            <img
                                                className="rounded-full h-12 w-12"
                                                src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                alt=""
                                            />
                                            {/* <Button theme={button_custom} size="xxs" color="transparentForTab"
                                            onClick={handleClose}>
                                            <IoClose className="text-slate-500 text-2xl" />
                                        </Button> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <AssignLead />
                        </Drawer.Items>
                    </Drawer>
                ) : (<></>)
            }

            {
                isDrawerOpen.addLead ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.addLead}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, addLead: !prev.addLead }))}
                        position="right"
                        className="w-full xl:w-1/2 lg:w-full md:w-full p-0"
                    >
                        <Drawer.Header
                            className="p-2"
                            title="Add New Lead"
                            titleIcon={
                                MdOutlineGroupAdd
                            }
                        />
                        <Drawer.Items>
                            <Add onClose={() => setIsDrawerOpen((prev) => ({ ...prev, addLead: !prev.addLead }))} />
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {
                isDrawerOpen.editLead ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.editLead}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, editLead: !prev.editLead }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <Drawer.Header
                            className="p-2"
                            title="Add New Lead"
                            titleIcon={
                                MdOutlineGroupAdd
                            }
                        />
                        <Drawer.Items>
                            <Edit />
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )
            }

        </Main >
    );
}
export default OldIndex;
