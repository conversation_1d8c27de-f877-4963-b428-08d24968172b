<?php

namespace App\Models\Lead;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class Lead extends Model
{
    use \App\Traits\AccessibleByUser;
    protected $table = 'lead';
    public $timestamps = false;

    protected $fillable = [
        'lead_source_id',
        'assignedTo_id',
        'createdBy_id',
        'referredBy_id',
        'remark',
        'status',
        'designation',
        'isCustomer',
        'isReseller',
        'datetime',
        'assignedAt',
        'name',
        'mobile',
        'email',
        'company',
        'city',
        'state',
        'country',
        'pinCode',
        'lead_for_id'
    ];

    public function leadSource()
    {
        return $this->belongsTo(LeadSource::class, 'lead_source_id', 'id');
    }
    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assignedTo_id', 'id');
    }
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'createdBy_id', 'id');
    }
    public function referredBy()
    {
        return $this->belongsTo(User::class, 'referredBy_id', 'id');
    }
    public function leadContacts()
    {
        return $this->hasMany(LeadContact::class, "lead_id", "id");
    }
    public function leadPrimaryContact()
    {
        return $this->hasOne(LeadContact::class, "lead_id", "id")->where("isPrimary", 1);
    }
    public function leadBusiness()
    {
        return $this->hasMany(LeadBusiness::class, "lead_id", "id");
    }
    public function leadPrimaryBusiness()
    {
        return $this->hasOne(LeadBusiness::class, "lead_id", "id")->where("isPrimary", 1);
    }
    public function leadFollowUp()
    {
        return $this->hasMany(LeadFollowUp::class, "lead_id", "id");
    }
    public function leadNextFollowup()
    {
        return $this->hasOne(LeadFollowup::class, 'lead_id')->latest('id');
    }
}
