import { useForm } from "@inertiajs/react";
import { Button, Label, Radio, Textarea } from "flowbite-react";
import { IoMdAdd } from "react-icons/io";
import { IoCloseSharp } from "react-icons/io5";
import { TiCamera } from "react-icons/ti";
import { button_custom, textarea_custom } from "../Helpers/DesignHelper";
import DropZone from "@/Components/DropZone";
import { FaRegImage } from "react-icons/fa6";
import useFetch from "@/Global/useFetch";
import React, { useState, useCallback, useEffect } from "react";
import SearchDropdown from "@/Components/HelperComponents/SearchDropdown";
import { IoMdClose } from "react-icons/io";


// Component for displaying selected items as chips in input field
const SelectedItemsDisplay = React.memo(({ selectedItems, onRemove, placeholder }) => {
  if (!selectedItems || selectedItems.length === 0) {
    return <span className="text-gray-500">{placeholder}</span>;
  }

  return (
    <div className="flex flex-wrap gap-1 py-1">
      {selectedItems.map((item) => (
        <div
          key={item.id}
          className="inline-flex items-center gap-1 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-md border border-blue-200"
        >
          <span className="max-w-24 truncate">{item.name}</span>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onRemove(item.id);
            }}
            className="ml-1 text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
          >
            <IoMdClose className="w-3 h-3" />
          </button>
        </div>
      ))}
    </div>
  );
});

function Edit({ onClose, leadId }) {

  // states decalration
  const [dropdownsToggle, setDropdownsToggle] = useState({
    leadSource: false,
    leadFor: false,
    assignTo: false,
  });

  // fetch required data for form 
  const { data: leadData, loading: leadLoading, leadError } = useFetch(route('leadsAndCustomers.leads.edit', { lead: leadId }));
  const { data: leadSourceData, loading: leadSourceLoading, leadSourceError } = useFetch(route('leads.getLeadSource'));
  const { data: leadForData, loading: leadForLoading, leadForError } = useFetch(route('leads.getLeadFor'));
  const { data: leadAgentsData, loading: leadAgentsLoading, leadAgentsError } = useFetch(route('leads.getLeadAgents'));

  console.log("leadData: ", leadData);

  const formFields = [
    {
      name: "name",
      type: "input",
      inputType: "text",
      label: "Name",
      required: true,
      placeholder: "John Smith",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_primary_contact?.name || "",
      position: 1
    },
    {
      name: "company",
      type: "input",
      inputType: "text",
      label: "Company",
      placeholder: "Enter company name",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_primary_business?.name || "",
      position: 2
    },
    {
      name: "mobile",
      type: "input",
      inputType: "tel",
      label: "Mobile Number",
      required: true,
      placeholder: "************",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_primary_contact?.mobile || "",
      position: 3
    },
    {
      name: "email",
      type: "input",
      inputType: "email",
      label: "Email",
      placeholder: "<EMAIL>",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_primary_contact?.email || "",
      position: 4
    },
    {
      name: "country",
      type: "input",
      label: "Country",
      placeholder: "Select country",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_primary_business?.country || "",
      position: 5
    },
    {
      name: "state",
      type: "input",
      label: "State",
      placeholder: "Enter Your State..",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_primary_business?.state || "",
      position: 6
    },
    {
      name: "city",
      type: "input",
      label: "City",
      placeholder: "Select account manager",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_primary_business?.city || "",
      position: 7
    },
    {
      name: "pincode",
      type: "input",
      inputType: "text",
      label: "Pincode",
      placeholder: "Enter pincode",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_primary_business?.pincode || "",
      position: 8
    },
    {
      name: "leadSource",
      type: "select",
      label: "Lead Source",
      placeholder: "Select lead source",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_source.id || "",
      options: leadSourceData?.data || [],
      position: 9
    },
    {
      name: "leadFor",
      type: "select",
      label: "Lead For",
      placeholder: "Select products",
      width: " col-span-1 ",
      value: leadData?.collection?.lead_for_id || [],
      options: leadForData?.data || [],
      multiple: true, // Enable multi-select for products
      position: 10
    },
    {
      name: "assignTo",
      type: "select",
      label: "Assign To",
      placeholder: "Select Agent",
      width: "col-span-2",
      value: leadData?.collection?.assignedTo_id || "",
      options: leadAgentsData?.data || [],
      position: 11
    },
    {
      name: "remark",
      type: "textarea",
      label: "Remark",
      placeholder: "Enter remarks or notes",
      width: " lg:col-span-2 col-span-2 ",
      value: leadData?.collection?.remark || "",
      rows: 4,
      position: 12
    },

  ].sort((a, b) => a.position - b.position);


  const { data, setData, post, processing, errors } = useForm({});

  useEffect(() => {
    if (leadData?.collection) {
      const initialData = formFields.reduce((acc, field) => {
        acc[field.name] = field.value ?? null;
        return acc;
      }, {});
      setData(initialData);
    }
  }, [leadData]);

  console.log("form Data: ", data);


  // input change handle
  const handleChange = useCallback((e) => {
    const key = e.target.name;
    const value = e.target.value
    setData(values => ({
      ...values,
      [key]: value,
    }))
  }, [setData]);


  // dropdown toggle handler
  const handleDropdownToggle = useCallback((fieldName) => {
    setDropdownsToggle(prev => ({
      leadSource: false,
      leadFor: false,
      assignTo: false,
      [fieldName]: !prev[fieldName]
    }));
  }, []);

  // dropdown close handler
  const handleDropdownClose = useCallback((fieldName) => {
    setDropdownsToggle(prev => ({
      ...prev,
      [fieldName]: false
    }));
  }, []);

  // dropdown selection handler
  const handleDropdownSelection = useCallback((fieldName, selected) => {

    setData(fieldName, selected);
    // Close dropdown after selection for single select only
    if (!Array.isArray(selected)) {
      setDropdownsToggle(prev => ({
        ...prev,
        [fieldName]: false
      }));
    }
  }, [setData]);

  // function to remove selected item from multi-select
  const removeSelectedItem = useCallback((fieldName, itemId) => {
    const currentValue = data[fieldName];
    if (Array.isArray(currentValue)) {
      const updated = currentValue.filter(item => item.id !== itemId);
      setData(fieldName, updated);
    }
  }, [data, setData]);

  // form submit handle 
  const handleAddSubmit = (e) => {
    e.preventDefault();

    post(route('leadsAndCustomers.leads.store'), {
      onSuccess: () => {
        onClose();
      },
    });
  };

  // Memoize display values for select fields to prevent unnecessary recalculations
  const getDisplayValue = useCallback((fieldName, options, isMultiple = false) => {
    const selectedValue = data[fieldName];
    if (!selectedValue) return '';

    // For multi-select, return null to show chips instead
    if (isMultiple && Array.isArray(selectedValue)) {
      return null;
    }

    if (typeof selectedValue === 'object' && selectedValue.name) {
      return selectedValue.name;
    }

    const option = options.find(opt => opt.id === selectedValue);
    return option ? option.name : '';
  }, [data]);


  return (
    <form onSubmit={handleAddSubmit}>
      <div className="bg-white px-4 py-4 rounded-lg pt-5">
        {/* -------------------User Image & File Upload------------------ */}
        <div className="flex gap-5 sm:flex-row lg:gap-7 items-center ">

          {data?.ProfilePic && data?.ProfilePic[0] ? (
            <div className="relative">
              <div className="h-24 w-24 rounded-full flex align-middle overflow-hidden">
                <img
                  src={URL.createObjectURL(data.ProfilePic[0])}
                  alt="Profile"
                  className="w-full h-full object-fit rounded-full img-fluid"
                />
              </div>
              <button
                onClick={() => setData('ProfilePic', null)}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
              >
                <IoCloseSharp className="text-sm" />
              </button>
            </div>
          ) : (

            <div className="h-24 w-24 bg-gray-600 rounded-full flex align-middle overflow-hidden">
              <div className="m-auto">
                <TiCamera className="text-gray-300 text-3xl " />
              </div>
            </div>
          )}
          <div id="dropzone-file" className="mt-3">
            <div className="border-2 border-dashed rounded-lg flex cursor-pointer ">
              <div className="self-center ms-3">
                <FaRegImage className="text-2xl text-gray-400 " />
              </div>
              <DropZone updateFormData={(file) => setData('ProfilePic', file)} padding="p-2" dropzoneParams={{
                accept: {
                  'image/jpeg': [],
                  'image/png': []
                },
              }}
              />
            </div>

            <div className="">
              <h4 className="text-gray-500 dark:text-gray-400 pt-1.5 ps-0.5 lg:text-[13px] md:text-xs text-xs font-medium">
                Accepted file types: .jpg, jpeg, .png
                <br></br>
                Note: For best results, upload a square image at
                least 200 px by 200 px.
              </h4>
            </div>
          </div>
        </div>
        <div className="mt-3 w-full">
          {errors["ProfilePic"] && <div className="text-sm text-red-700">{errors["ProfilePic"]}</div>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {formFields.map((field, index) => (
            <div key={index} className={field.width}>
              <label className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                {field.label}
                {field.required && <span className="text-red-700 ms-1">*</span>}
              </label>
              {field.type === 'input' && (
                <>
                  <input
                    name={field.name}
                    type={field.inputType}
                    className="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    placeholder={field.placeholder}
                    required={field.required}
                    onChange={handleChange}
                    value={data[field.name] || ''}
                  />
                  {errors[field.name] && <div className="text-sm text-red-700">{errors[field.name]}</div>}
                </>
              )}
              {field.type === 'select' && (
                <>
                  <div
                    className="bg-white bg border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 min-h-[42px]  cursor-pointer h-14 overflow-auto"
                    onClick={() => handleDropdownToggle(field.name)}
                  >
                    {field.multiple && Array.isArray(data[field.name]) && data[field.name].length > 0 ? (
                      <SelectedItemsDisplay
                        selectedItems={data[field.name]}
                        onRemove={(itemId) => removeSelectedItem(field.name, itemId)}
                        placeholder={field.placeholder}
                      />
                    ) : (
                      <div className="flex items-center h-full">
                        {!field.multiple && getDisplayValue(field.name, field.options, field.multiple) ? (
                          <span>{getDisplayValue(field.name, field.options, field.multiple)}</span>
                        ) : (
                          <span className="text-gray-500">{field.placeholder}</span>
                        )}
                      </div>
                    )}
                  </div>

                  {dropdownsToggle[field.name] && (
                    <SearchDropdown
                      data={field.options}
                      multiple={field.multiple || false}
                      showRadio={true}
                      showImage={true}
                      onChange={(selected) => handleDropdownSelection(field.name, selected)}
                      onClose={() => handleDropdownClose(field.name)}
                      initialSelected={field.multiple ? (data[field.name] || []) : (data[field.name] ? [data[field.name]] : [])}
                    />
                  )}

                  {errors[field.name] && <div className="text-sm text-red-700">{errors[field.name]}</div>}
                </>
              )}
              {field.type === 'textarea' && (
                <>
                  <Textarea
                    name={field.name}
                    theme={textarea_custom}
                    placeholder={field.placeholder}
                    rows={field.rows}
                    className="w-full"
                    onChange={handleChange}
                    value={data[field.name] || ''}
                  />
                  {errors[field.name] && <div className="text-sm text-red-700">{errors[field.name]}</div>}
                </>
              )}
              {field.type === 'radio' && (
                <>
                  <div className="flex flex-wrap gap-4">
                    {field.options.map((option, optIndex) => (
                      <div className="flex items-center gap-2" key={optIndex}>
                        <Radio id={"radio-" + optIndex} name={field.name} value={option.value} />
                        <Label htmlFor={"radio-" + optIndex} >{option.name}</Label>
                      </div>
                    ))}
                  </div>
                  {errors[field.name] && <div className="text-sm text-red-700">{errors[field.name]}</div>}
                </>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-end gap-5 py-3">
          <Button
            theme={button_custom}
            color="failure"
            className="rounded"
            size="xs"
            type="button"
            onClick={() => onClose()}
          >
            <div className="flex items-center gap-2">
              <IoCloseSharp className="text-xl" />
              <span className="text-sm">Cancel</span>
            </div>
          </Button>
          <Button
            theme={button_custom}
            color="blue"
            className="rounded"
            size="xs"
            isProcessing={processing}
            disabled={processing}
            type="submit"
          >
            <div className="flex items-center gap-2">
              <IoMdAdd className="text-sm" />
              <span className="text-sm">Add Lead</span>
            </div>
          </Button>
        </div>
      </div>
    </form>
  );
}

export default Edit;
